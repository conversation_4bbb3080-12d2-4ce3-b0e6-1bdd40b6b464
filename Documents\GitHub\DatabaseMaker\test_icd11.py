#!/usr/bin/env python3
"""
Test script for analyzing the actual ICD-11 multilingual CSV file.
"""

from multilingual_config import MultilingualCSVProcessor

def test_icd11_analysis():
    """Test analysis of the actual ICD-11 CSV file."""
    filename = "icd11_multilingual_terms_normalized_6lang - icd11_multilingual_terms_normalized_6lang.csv"
    
    print("=== ICD-11 Multilingual CSV Analysis ===")
    print(f"File: {filename}")
    print()
    
    try:
        # Initialize specialized processor
        processor = MultilingualCSVProcessor()
        
        # Analyze the file
        analysis = processor.analyze_multilingual_structure(filename)
        
        print(f"Total rows: {analysis['total_rows']}")
        print(f"Total columns: {analysis['total_columns']}")
        print()
        
        # Show multilingual insights
        multilingual_info = analysis['multilingual_info']
        
        print("=== Multilingual Structure Analysis ===")
        print(f"Languages detected: {multilingual_info['total_languages']}")
        print(f"Has normalized versions: {multilingual_info['has_normalized_versions']}")
        print(f"Primary key candidates: {multilingual_info['primary_key_candidates']}")
        print()
        
        print("Language columns:")
        for lang_col in multilingual_info['language_columns']:
            print(f"  - {lang_col['column']} ({lang_col['language']})")
        
        print()
        print("Normalized columns:")
        for norm_col in multilingual_info['normalized_columns']:
            print(f"  - {norm_col['column']} ({norm_col['language']})")
        
        print()
        print("Code columns:")
        for code_col in multilingual_info['code_columns']:
            print(f"  - {code_col}")
        
        print()
        print("=== Column Details ===")
        print(f"{'Column Name':<20} {'PostgreSQL Type':<20} {'Nullable':<10} {'Unique':<10}")
        print("-" * 70)
        
        for col_name, col_info in analysis['columns'].items():
            nullable = "Yes" if col_info['nullable'] else "No"
            unique = "Yes" if col_info['is_unique'] else "No"
            print(f"{col_name:<20} {col_info['postgresql_type']:<20} {nullable:<10} {unique:<10}")
        
        print()
        print("=== Optimization Suggestions ===")
        for suggestion in multilingual_info['optimization_suggestions']:
            print(f"• {suggestion}")
        
        print()
        print("=== Recommended Table Creation ===")
        print("To create the optimized table, run:")
        print(f'py -c "')
        print(f'from multilingual_config import MultilingualCSVProcessor')
        print(f'processor = MultilingualCSVProcessor()')
        print(f'result = processor.create_multilingual_table(')
        print(f'    csv_file_path=\"{filename}\",')
        print(f'    table_name=\"icd11_terms\",')
        print(f'    add_search_indexes=True')
        print(f')')
        print(f'print(\"Table created:\", result[\"success\"])')
        print(f'"')
        
        return True
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_icd11_analysis()
