#!/usr/bin/env python3
"""
Complete testing script for CSV to database migration.
Tests the migration process and verifies data integrity.
"""

import os
from database_connection import DatabaseConnection
from migrate_csv_to_schema import CSVToSchemaConverter
from future_proof_schema import FutureProofSchemaGenerator

def test_database_setup():
    """Test database connection and setup."""
    print("=== Testing Database Setup ===")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ No .env file found!")
        print("📝 To fix this:")
        print("   1. Copy .env.example to .env")
        print("   2. Edit .env with your PostgreSQL credentials")
        print("   3. Make sure PostgreSQL is running")
        print()
        print("Example .env content:")
        print("DB_HOST=localhost")
        print("DB_PORT=5432") 
        print("DB_NAME=icd11_db")
        print("DB_USER=your_username")
        print("DB_PASSWORD=your_password")
        return False
    
    # Test database connection
    try:
        db = DatabaseConnection()
        if db.connect():
            print("✅ Database connection successful!")
            
            # Test basic query
            result = db.execute_query("SELECT version();")
            if result:
                print(f"✅ PostgreSQL version: {result[0][0][:50]}...")
            
            db.disconnect()
            return True
        else:
            print("❌ Database connection failed!")
            return False
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        print("💡 Common fixes:")
        print("   - Check if PostgreSQL is running")
        print("   - Verify credentials in .env file")
        print("   - Ensure database exists")
        return False

def test_csv_file():
    """Test if CSV file exists and is readable."""
    print("\n=== Testing CSV File ===")
    
    csv_file = "icd11_multilingual_terms_normalized_6lang - icd11_multilingual_terms_normalized_6lang.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        print("📁 Available files:")
        for file in os.listdir('.'):
            if file.endswith('.csv'):
                print(f"   - {file}")
        return False, None
    
    try:
        import pandas as pd
        df = pd.read_csv(csv_file)
        print(f"✅ CSV file loaded successfully!")
        print(f"   📊 Rows: {len(df)}")
        print(f"   📊 Columns: {len(df.columns)}")
        print(f"   📊 Columns: {list(df.columns)}")
        return True, csv_file
        
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False, None

def run_migration_test(csv_file):
    """Run the actual migration and test results."""
    print("\n=== Running Migration Test ===")
    
    try:
        # Initialize converter
        converter = CSVToSchemaConverter()
        
        print("🚀 Starting migration...")
        results = converter.migrate_csv_to_schema(csv_file, create_schema=True)
        
        if results['success']:
            print("✅ Migration completed successfully!")
            print(f"   📊 Records processed: {results['records_processed']}")
            print(f"   📊 Chapters created: {results['chapters_created']}")
            print(f"   📊 Terms created: {results['terms_created']}")
            print(f"   📊 Translations created: {results['translations_created']}")
            print(f"   📊 Normalizations created: {results['normalizations_created']}")
            return True
        else:
            print("❌ Migration failed!")
            for error in results['errors']:
                print(f"   ❌ {error}")
            return False
            
    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False

def verify_data_integrity():
    """Verify that data was migrated correctly."""
    print("\n=== Verifying Data Integrity ===")
    
    try:
        db = DatabaseConnection()
        if not db.connect():
            print("❌ Cannot connect to database for verification")
            return False
        
        # Test queries to verify data
        tests = [
            ("Languages table", "SELECT COUNT(*) FROM languages;"),
            ("Chapters table", "SELECT COUNT(*) FROM chapters;"),
            ("Terms table", "SELECT COUNT(*) FROM terms;"),
            ("Translations table", "SELECT COUNT(*) FROM term_translations;"),
            ("Normalizations table", "SELECT COUNT(*) FROM term_normalizations;"),
            ("Complete view", "SELECT COUNT(*) FROM v_terms_complete;"),
        ]
        
        all_passed = True
        for test_name, query in tests:
            try:
                result = db.execute_query(query)
                count = result[0][0] if result else 0
                print(f"✅ {test_name}: {count} records")
                
                if count == 0 and "table" in test_name.lower():
                    print(f"   ⚠️  Warning: {test_name} is empty")
                    
            except Exception as e:
                print(f"❌ {test_name}: Error - {e}")
                all_passed = False
        
        # Test sample data retrieval
        print("\n📋 Sample Data Check:")
        try:
            sample_query = """
            SELECT chapter_code, term_code, language_code, translation_text 
            FROM v_terms_primary 
            LIMIT 5;
            """
            results = db.execute_query(sample_query)
            
            if results:
                print("✅ Sample translations:")
                for row in results:
                    print(f"   {row[0]}.{row[1]} ({row[2]}): {row[3][:50]}...")
            else:
                print("❌ No sample data found")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Sample data error: {e}")
            all_passed = False
        
        db.disconnect()
        return all_passed
        
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def test_search_functionality():
    """Test search capabilities of the new schema."""
    print("\n=== Testing Search Functionality ===")
    
    try:
        db = DatabaseConnection()
        if not db.connect():
            print("❌ Cannot connect to database for search test")
            return False
        
        # Test different search scenarios
        search_tests = [
            ("Find specific term", "SELECT * FROM v_terms_primary WHERE term_code = 'TERM001' LIMIT 1;"),
            ("Search by text", "SELECT * FROM v_terms_primary WHERE translation_text ILIKE '%computer%' LIMIT 3;"),
            ("Language-specific search", "SELECT * FROM v_terms_primary WHERE language_code = 'en' LIMIT 3;"),
            ("Chapter search", "SELECT DISTINCT chapter_code FROM v_terms_primary;"),
        ]
        
        for test_name, query in search_tests:
            try:
                results = db.execute_query(query)
                count = len(results) if results else 0
                print(f"✅ {test_name}: {count} results")
                
                if results and count > 0:
                    # Show first result as example
                    first_result = results[0]
                    if len(first_result) >= 4:
                        print(f"   📝 Example: {first_result[0]}.{first_result[1]} ({first_result[2]})")
                    
            except Exception as e:
                print(f"❌ {test_name}: Error - {e}")
        
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Search test error: {e}")
        return False

def main():
    """Run complete migration test suite."""
    print("🧪 ICD-11 CSV to Database Migration Test")
    print("=" * 50)
    
    # Step 1: Test database setup
    if not test_database_setup():
        print("\n❌ Database setup failed. Please fix database connection first.")
        return
    
    # Step 2: Test CSV file
    csv_ok, csv_file = test_csv_file()
    if not csv_ok:
        print("\n❌ CSV file test failed. Please check your CSV file.")
        return
    
    # Step 3: Run migration
    if not run_migration_test(csv_file):
        print("\n❌ Migration failed. Please check the errors above.")
        return
    
    # Step 4: Verify data integrity
    if not verify_data_integrity():
        print("\n❌ Data verification failed. Migration may be incomplete.")
        return
    
    # Step 5: Test search functionality
    if not test_search_functionality():
        print("\n⚠️  Search tests failed, but data migration was successful.")
    
    # Success summary
    print("\n" + "=" * 50)
    print("🎉 MIGRATION TEST COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print("✅ Your CSV data has been successfully migrated to the future-proof schema!")
    print()
    print("📊 What was created:")
    print("   • Normalized database schema with 6 tables")
    print("   • Performance indexes for fast search")
    print("   • Convenience views for easy querying")
    print("   • All your CSV data properly structured")
    print()
    print("🔍 Try these queries to explore your data:")
    print("   py -c \"from database_connection import DatabaseConnection; db=DatabaseConnection(); db.connect(); print(db.execute_query('SELECT COUNT(*) FROM v_terms_complete;')[0][0], 'total term-language combinations')\"")
    print()
    print("📖 See FUTURE_PROOF_SCHEMA.md for detailed documentation")

if __name__ == "__main__":
    main()
