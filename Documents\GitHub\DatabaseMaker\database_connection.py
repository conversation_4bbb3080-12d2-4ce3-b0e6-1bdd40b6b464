"""
Database connection module for PostgreSQL operations.
Handles connection management, error handling, and basic database operations.
"""

import os
import logging
from typing import Optional, Dict, Any, List, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseConnection:
    """Manages PostgreSQL database connections and operations."""
    
    def __init__(self, config: Optional[Dict[str, str]] = None):
        """
        Initialize database connection with configuration.
        
        Args:
            config: Optional database configuration dict. If None, uses environment variables.
        """
        if config:
            self.config = config
        else:
            self.config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '5432'),
                'database': os.getenv('DB_NAME'),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD'),
                'sslmode': os.getenv('DB_SSLMODE', 'prefer')
            }
        
        self.connection = None
        self.cursor = None
        
        # Validate required configuration
        required_fields = ['database', 'user', 'password']
        missing_fields = [field for field in required_fields if not self.config.get(field)]
        if missing_fields:
            raise ValueError(f"Missing required database configuration: {', '.join(missing_fields)}")
    
    def connect(self) -> bool:
        """
        Establish connection to PostgreSQL database.
        
        Returns:
            bool: True if connection successful, False otherwise.
        """
        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password'],
                sslmode=self.config['sslmode']
            )
            self.cursor = self.connection.cursor(cursor_factory=RealDictCursor)
            logger.info(f"Successfully connected to database: {self.config['database']}")
            return True
        except psycopg2.Error as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection and cursor."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> Optional[List[Dict]]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Optional query parameters
            
        Returns:
            List of dictionaries representing query results, or None if error.
        """
        try:
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except psycopg2.Error as e:
            logger.error(f"Query execution failed: {e}")
            self.connection.rollback()
            return None
    
    def execute_command(self, command: str, params: Optional[tuple] = None) -> bool:
        """
        Execute a non-SELECT command (INSERT, UPDATE, DELETE, CREATE, etc.).
        
        Args:
            command: SQL command string
            params: Optional command parameters
            
        Returns:
            bool: True if command executed successfully, False otherwise.
        """
        try:
            self.cursor.execute(command, params)
            self.connection.commit()
            logger.info("Command executed successfully")
            return True
        except psycopg2.Error as e:
            logger.error(f"Command execution failed: {e}")
            self.connection.rollback()
            return False
    
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            bool: True if table exists, False otherwise.
        """
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = %s
        );
        """
        result = self.execute_query(query, (table_name,))
        return result[0]['exists'] if result else False
    
    def get_table_columns(self, table_name: str) -> Optional[List[Dict]]:
        """
        Get column information for a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of dictionaries with column information, or None if error.
        """
        query = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = %s
        ORDER BY ordinal_position;
        """
        return self.execute_query(query, (table_name,))
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
