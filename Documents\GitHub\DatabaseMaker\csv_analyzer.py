"""
CSV analyzer module for automatic data type detection and structure analysis.
Analyzes CSV files to determine optimal PostgreSQL column types and constraints.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import re

logger = logging.getLogger(__name__)


class CSVAnalyzer:
    """Analyzes CSV files to determine structure and data types for PostgreSQL."""
    
    def __init__(self, sample_size: int = 1000):
        """
        Initialize CSV analyzer.
        
        Args:
            sample_size: Number of rows to sample for type detection (0 = all rows)
        """
        self.sample_size = sample_size
        
        # PostgreSQL type mapping
        self.type_mapping = {
            'integer': 'INTEGER',
            'float': 'NUMERIC',
            'boolean': 'BOOLEAN',
            'datetime': 'TIMESTAMP',
            'date': 'DATE',
            'string': 'TEXT'
        }
    
    def analyze_csv(self, file_path: str, delimiter: str = ',', encoding: str = 'utf-8') -> Dict[str, Any]:
        """
        Analyze CSV file structure and data types.
        
        Args:
            file_path: Path to CSV file
            delimiter: CSV delimiter character
            encoding: File encoding
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Read CSV file
            df = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding)
            logger.info(f"Successfully loaded CSV: {len(df)} rows, {len(df.columns)} columns")
            
            # Sample data if specified
            if self.sample_size > 0 and len(df) > self.sample_size:
                sample_df = df.sample(n=self.sample_size, random_state=42)
                logger.info(f"Using sample of {self.sample_size} rows for analysis")
            else:
                sample_df = df
            
            # Analyze each column
            column_info = {}
            for column in df.columns:
                column_info[column] = self._analyze_column(sample_df[column], column)
            
            # Generate analysis summary
            analysis = {
                'file_path': file_path,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': column_info,
                'sample_size': len(sample_df),
                'has_header': True,  # Assuming first row is header
                'delimiter': delimiter,
                'encoding': encoding
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze CSV file {file_path}: {e}")
            raise
    
    def _analyze_column(self, series: pd.Series, column_name: str) -> Dict[str, Any]:
        """
        Analyze a single column to determine its characteristics.
        
        Args:
            series: Pandas series representing the column
            column_name: Name of the column
            
        Returns:
            Dictionary with column analysis results
        """
        # Basic statistics
        total_count = len(series)
        null_count = series.isnull().sum()
        non_null_count = total_count - null_count
        null_percentage = (null_count / total_count) * 100 if total_count > 0 else 0
        
        # Determine data type
        detected_type, pg_type, max_length = self._detect_data_type(series)
        
        # Check for potential primary key
        is_unique = series.nunique() == non_null_count if non_null_count > 0 else False
        
        # Sample values (non-null)
        sample_values = series.dropna().head(5).tolist()
        
        column_info = {
            'name': column_name,
            'detected_type': detected_type,
            'postgresql_type': pg_type,
            'max_length': max_length,
            'total_count': total_count,
            'null_count': null_count,
            'non_null_count': non_null_count,
            'null_percentage': round(null_percentage, 2),
            'is_unique': is_unique,
            'sample_values': sample_values,
            'nullable': null_count > 0
        }
        
        return column_info
    
    def _detect_data_type(self, series: pd.Series) -> Tuple[str, str, Optional[int]]:
        """
        Detect the most appropriate data type for a column.
        
        Args:
            series: Pandas series to analyze
            
        Returns:
            Tuple of (detected_type, postgresql_type, max_length)
        """
        # Remove null values for analysis
        non_null_series = series.dropna()
        
        if len(non_null_series) == 0:
            return 'string', 'TEXT', None
        
        # Convert to string for pattern analysis
        str_series = non_null_series.astype(str)
        
        # Check for boolean
        if self._is_boolean(str_series):
            return 'boolean', 'BOOLEAN', None
        
        # Check for integer
        if self._is_integer(non_null_series):
            return 'integer', 'INTEGER', None
        
        # Check for float
        if self._is_float(non_null_series):
            return 'float', 'NUMERIC', None
        
        # Check for datetime
        if self._is_datetime(str_series):
            return 'datetime', 'TIMESTAMP', None
        
        # Check for date
        if self._is_date(str_series):
            return 'date', 'DATE', None
        
        # Default to string with appropriate length
        max_length = str_series.str.len().max()
        
        # Choose appropriate string type based on length
        if max_length <= 255:
            pg_type = f'VARCHAR({max_length})'
        else:
            pg_type = 'TEXT'
        
        return 'string', pg_type, max_length
    
    def _is_boolean(self, str_series: pd.Series) -> bool:
        """Check if series contains boolean values."""
        boolean_values = {'true', 'false', '1', '0', 'yes', 'no', 'y', 'n', 't', 'f'}
        unique_values = set(str_series.str.lower().unique())
        return unique_values.issubset(boolean_values) and len(unique_values) <= 2
    
    def _is_integer(self, series: pd.Series) -> bool:
        """Check if series contains integer values."""
        try:
            # Try to convert to numeric
            numeric_series = pd.to_numeric(series, errors='coerce')
            # Check if conversion was successful for most values
            non_null_original = series.dropna()
            non_null_numeric = numeric_series.dropna()

            # If most values couldn't be converted to numeric, it's not integer
            if len(non_null_numeric) < len(non_null_original) * 0.8:
                return False

            # Check if all successfully converted values are integers
            return len(non_null_numeric) > 0 and non_null_numeric.apply(lambda x: x == int(x)).all()
        except:
            return False
    
    def _is_float(self, series: pd.Series) -> bool:
        """Check if series contains float values."""
        try:
            # Try to convert to numeric
            numeric_series = pd.to_numeric(series, errors='coerce')
            # Check if conversion was successful for most values
            non_null_original = series.dropna()
            non_null_numeric = numeric_series.dropna()

            # If most values couldn't be converted to numeric, it's not float
            if len(non_null_numeric) < len(non_null_original) * 0.8:
                return False

            # If conversion successful and not all integers, it's float
            return len(non_null_numeric) > 0 and not self._is_integer(series)
        except:
            return False
    
    def _is_datetime(self, str_series: pd.Series) -> bool:
        """Check if series contains datetime values."""
        try:
            # Try to parse as datetime
            datetime_series = pd.to_datetime(str_series, errors='coerce')
            # If most values can be parsed as datetime, consider it datetime
            success_rate = datetime_series.notna().sum() / len(str_series)
            return success_rate > 0.8
        except:
            return False
    
    def _is_date(self, str_series: pd.Series) -> bool:
        """Check if series contains date values (without time component)."""
        try:
            # Check for common date patterns
            date_patterns = [
                r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD
                r'^\d{2}/\d{2}/\d{4}$',  # MM/DD/YYYY
                r'^\d{2}-\d{2}-\d{4}$',  # MM-DD-YYYY
            ]
            
            for pattern in date_patterns:
                if str_series.str.match(pattern).sum() / len(str_series) > 0.8:
                    return True
            
            return False
        except:
            return False
    
    def generate_create_table_sql(self, analysis: Dict[str, Any], table_name: str) -> str:
        """
        Generate CREATE TABLE SQL statement based on analysis.
        
        Args:
            analysis: Analysis results from analyze_csv
            table_name: Name for the new table
            
        Returns:
            SQL CREATE TABLE statement
        """
        columns = []
        
        for column_name, column_info in analysis['columns'].items():
            # Clean column name (replace spaces and special chars with underscores)
            clean_name = re.sub(r'[^\w]', '_', column_name.lower())
            
            # Build column definition
            column_def = f'"{clean_name}" {column_info["postgresql_type"]}'
            
            # Add NOT NULL constraint if no nulls found
            if not column_info['nullable']:
                column_def += ' NOT NULL'
            
            columns.append(column_def)
        
        # Create the SQL statement
        sql = f'CREATE TABLE IF NOT EXISTS "{table_name}" (\n'
        sql += ',\n'.join(f'    {col}' for col in columns)
        sql += '\n);'
        
        return sql
