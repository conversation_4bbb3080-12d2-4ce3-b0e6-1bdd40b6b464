# Future-Proof Schema for ICD-11 Multilingual Terminology

## Overview

This document describes a future-proof, normalized database schema designed for your ICD-11 multilingual terminology data. The schema transforms your flat CSV structure into a scalable, maintainable relational database that can handle growth and evolving requirements.

## Schema Architecture

### 🏗️ **Core Design Principles**

1. **Normalization**: Eliminates data duplication and ensures consistency
2. **Scalability**: Supports unlimited languages and translations
3. **Versioning**: Tracks changes over time
4. **Performance**: Optimized indexes for fast search
5. **Flexibility**: Accommodates future requirements
6. **Integrity**: Foreign key constraints ensure data consistency

### 📊 **Table Structure**

```
┌─────────────┐    ┌──────────────────┐    ┌─────────────┐
│  languages  │    │     chapters     │    │    terms    │
├─────────────┤    ├──────────────────┤    ├─────────────┤
│ language_id │    │ chapter_id (PK)  │    │ term_id (PK)│
│ language_code│    │ chapter_code     │    │ term_code   │
│ language_name│    │ chapter_number   │    │ chapter_id  │──┐
│ iso_639_1   │    │ is_active        │    │ icd11_code  │  │
│ iso_639_3   │    │ created_at       │    │ term_version│  │
│ is_active   │    │ updated_at       │    │ is_active   │  │
│ created_at  │    └──────────────────┘    │ created_at  │  │
│ updated_at  │                            │ updated_at  │  │
└─────────────┘                            └─────────────┘  │
       │                                           │         │
       │                                           │         │
       │    ┌──────────────────────────────────────┘         │
       │    │                                                │
       │    ▼                                                │
       │ ┌─────────────────────┐                             │
       └─│ term_translations   │                             │
         ├─────────────────────┤                             │
         │ translation_id (PK) │                             │
         │ term_id (FK)        │─────────────────────────────┘
         │ language_id (FK)    │
         │ translation_text    │
         │ translation_quality │
         │ is_primary          │
         │ created_at          │
         │ updated_at          │
         └─────────────────────┘
                    │
                    │
                    ▼
         ┌─────────────────────┐
         │ term_normalizations │
         ├─────────────────────┤
         │ normalization_id    │
         │ translation_id (FK) │
         │ normalized_text     │
         │ normalization_type  │
         │ normalization_rules │
         │ created_at          │
         │ updated_at          │
         └─────────────────────┘
```

## Table Descriptions

### 1. **languages** - Language Reference
- **Purpose**: Master list of supported languages
- **Key Features**: ISO standard codes, activation status
- **Future-Proof**: Easy to add new languages

### 2. **chapters** - ICD-11 Chapters
- **Purpose**: Reference table for ICD-11 chapters
- **Key Features**: Unique chapter codes, optional numbering
- **Future-Proof**: Supports new chapter structures

### 3. **terms** - Core Terms
- **Purpose**: Main terminology entries with versioning
- **Key Features**: Version tracking, activation status
- **Future-Proof**: Handles term evolution over time

### 4. **term_translations** - Language Translations
- **Purpose**: Translations of terms in different languages
- **Key Features**: Quality indicators, primary translation flags
- **Future-Proof**: Multiple translations per language, quality tracking

### 5. **term_normalizations** - Normalized Text
- **Purpose**: Optimized versions for search and matching
- **Key Features**: Multiple normalization types, rule tracking
- **Future-Proof**: Flexible normalization strategies

### 6. **schema_metadata** - Version Tracking
- **Purpose**: Track schema versions and data imports
- **Key Features**: Checksums, import tracking
- **Future-Proof**: Audit trail for all changes

## Benefits Over Flat CSV Structure

### ✅ **Advantages**

| Aspect | CSV Structure | Future-Proof Schema |
|--------|---------------|-------------------|
| **Data Duplication** | High (repeated codes) | None (normalized) |
| **New Languages** | Requires schema change | Just add to languages table |
| **Search Performance** | Table scan | Optimized indexes |
| **Data Integrity** | No constraints | Foreign key constraints |
| **Versioning** | Manual file management | Built-in version tracking |
| **Scalability** | Limited by file size | Database scalability |
| **Concurrent Access** | File locking issues | Database transactions |
| **Backup/Recovery** | File-based | Database backup tools |

### 🚀 **Performance Optimizations**

1. **Indexes for Fast Lookup**:
   - Primary key indexes on all tables
   - Composite indexes for common queries
   - Full-text search indexes (GIN)
   - Trigram indexes for fuzzy matching

2. **Query Optimization**:
   - Convenience views for common queries
   - Optimized join paths
   - Selective indexes with WHERE clauses

3. **Search Capabilities**:
   - Full-text search across all languages
   - Fuzzy matching with trigrams
   - Normalized text for better matching

## Usage Examples

### 🔍 **Common Queries**

```sql
-- Find all translations for a specific term
SELECT * FROM "v_terms_complete" 
WHERE "term_code" = 'TERM001' AND "chapter_code" = 'TECH001';

-- Search for terms containing "computer" in any language
SELECT DISTINCT "term_code", "chapter_code", "translation_text"
FROM "v_terms_primary"
WHERE "translation_text" ILIKE '%computer%';

-- Get all terms in a specific language
SELECT "term_code", "translation_text"
FROM "v_terms_primary"
WHERE "language_code" = 'en';

-- Find terms with fuzzy matching (requires pg_trgm)
SELECT "term_code", "translation_text", 
       similarity("translation_text", 'komputer') as sim
FROM "v_terms_primary"
WHERE "translation_text" % 'komputer'
ORDER BY sim DESC;
```

### 📝 **Adding New Data**

```sql
-- Add a new language
INSERT INTO "languages" ("language_code", "language_name", "iso_639_1")
VALUES ('zh', 'Chinese', 'zh');

-- Add translation for existing term
INSERT INTO "term_translations" ("term_id", "language_id", "translation_text")
SELECT t."term_id", l."language_id", '计算机'
FROM "terms" t, "languages" l
WHERE t."term_code" = 'TERM001' AND l."language_code" = 'zh';
```

## Migration Process

### 📥 **From CSV to Schema**

1. **Create Schema**: Run `future_proof_schema.py`
2. **Migrate Data**: Run `migrate_csv_to_schema.py`
3. **Verify Results**: Check data integrity and counts

```bash
# Create schema and migrate data
py migrate_csv_to_schema.py

# Verify migration
py -c "
from database_connection import DatabaseConnection
db = DatabaseConnection()
result = db.execute_query('SELECT COUNT(*) FROM v_terms_complete;')
print(f'Total term-language combinations: {result[0][0]}')
"
```

## Future Enhancements

### 🔮 **Planned Features**

1. **Synonym Support**: Link related terms
2. **Definition Storage**: Add term definitions
3. **Audit Logging**: Track all changes
4. **API Integration**: REST API for external access
5. **Machine Translation**: Automated translation suggestions
6. **Quality Scoring**: Advanced translation quality metrics

### 🛠️ **Extension Points**

- **Custom Normalization**: Add new normalization algorithms
- **Metadata Fields**: Additional term properties
- **Relationship Mapping**: Term hierarchies and relationships
- **Multi-version Support**: Handle multiple ICD versions simultaneously

## Maintenance

### 🔧 **Regular Tasks**

1. **Index Maintenance**: `REINDEX` for optimal performance
2. **Statistics Update**: `ANALYZE` for query optimization
3. **Backup Strategy**: Regular database backups
4. **Monitoring**: Query performance and usage patterns

### 📊 **Monitoring Queries**

```sql
-- Check data distribution
SELECT l."language_code", COUNT(*) as translation_count
FROM "term_translations" tr
JOIN "languages" l ON tr."language_id" = l."language_id"
GROUP BY l."language_code"
ORDER BY translation_count DESC;

-- Find missing translations
SELECT t."term_code", c."chapter_code", l."language_code"
FROM "terms" t
CROSS JOIN "languages" l
JOIN "chapters" c ON t."chapter_id" = c."chapter_id"
LEFT JOIN "term_translations" tr ON t."term_id" = tr."term_id" 
    AND l."language_id" = tr."language_id"
WHERE tr."translation_id" IS NULL
    AND t."is_active" = TRUE 
    AND l."is_active" = TRUE;
```

## Conclusion

This future-proof schema provides a solid foundation for your ICD-11 multilingual terminology database. It offers:

- **Immediate Benefits**: Better performance, data integrity, search capabilities
- **Long-term Value**: Scalability, maintainability, extensibility
- **Migration Path**: Smooth transition from CSV with full data preservation

The schema is designed to grow with your needs while maintaining optimal performance and data quality.
