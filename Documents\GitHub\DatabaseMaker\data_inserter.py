"""
Data insertion module for efficient bulk loading of CSV data into PostgreSQL.
Handles data transformation, validation, and batch insertion operations.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import re
from database_connection import DatabaseConnection

logger = logging.getLogger(__name__)


class DataInserter:
    """Handles efficient insertion of CSV data into PostgreSQL tables."""
    
    def __init__(self, db_connection: DatabaseConnection, batch_size: int = 1000):
        """
        Initialize data inserter.
        
        Args:
            db_connection: Active database connection instance
            batch_size: Number of rows to insert per batch
        """
        self.db = db_connection
        self.batch_size = batch_size
    
    def insert_csv_data(self, file_path: str, table_name: str, analysis: Dict[str, Any],
                       delimiter: str = ',', encoding: str = 'utf-8',
                       skip_errors: bool = False) -> Tuple[bool, Dict[str, Any]]:
        """
        Insert CSV data into PostgreSQL table.
        
        Args:
            file_path: Path to CSV file
            table_name: Target table name
            analysis: Analysis results from CSVAnalyzer
            delimiter: CSV delimiter
            encoding: File encoding
            skip_errors: Whether to skip rows with errors
            
        Returns:
            Tuple of (success, statistics)
        """
        try:
            # Read CSV file
            df = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding)
            logger.info(f"Loaded CSV file: {len(df)} rows, {len(df.columns)} columns")
            
            # Clean column names to match database schema
            df = self._clean_column_names(df, analysis)
            
            # Transform data according to analysis
            df_transformed = self._transform_data(df, analysis)
            
            # Insert data in batches
            stats = self._insert_batches(df_transformed, table_name, skip_errors)
            
            return True, stats
            
        except Exception as e:
            logger.error(f"Failed to insert CSV data: {e}")
            return False, {'error': str(e)}
    
    def _clean_column_names(self, df: pd.DataFrame, analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        Clean column names to match database schema.
        
        Args:
            df: Original DataFrame
            analysis: Analysis results
            
        Returns:
            DataFrame with cleaned column names
        """
        column_mapping = {}
        
        for original_name in df.columns:
            # Find corresponding clean name from analysis
            clean_name = self._clean_identifier(original_name)
            column_mapping[original_name] = clean_name
        
        df_renamed = df.rename(columns=column_mapping)
        logger.info(f"Renamed columns: {column_mapping}")
        
        return df_renamed
    
    def _transform_data(self, df: pd.DataFrame, analysis: Dict[str, Any]) -> pd.DataFrame:
        """
        Transform data according to detected types.
        
        Args:
            df: DataFrame with cleaned column names
            analysis: Analysis results
            
        Returns:
            Transformed DataFrame
        """
        df_transformed = df.copy()
        
        for original_column, column_info in analysis['columns'].items():
            clean_column = self._clean_identifier(original_column)
            
            if clean_column not in df_transformed.columns:
                continue
            
            try:
                df_transformed[clean_column] = self._transform_column(
                    df_transformed[clean_column], 
                    column_info
                )
            except Exception as e:
                logger.warning(f"Failed to transform column {clean_column}: {e}")
        
        return df_transformed
    
    def _transform_column(self, series: pd.Series, column_info: Dict[str, Any]) -> pd.Series:
        """
        Transform a single column based on its detected type.
        
        Args:
            series: Column data
            column_info: Column analysis information
            
        Returns:
            Transformed series
        """
        detected_type = column_info['detected_type']
        
        if detected_type == 'integer':
            return pd.to_numeric(series, errors='coerce').astype('Int64')
        
        elif detected_type == 'float':
            return pd.to_numeric(series, errors='coerce')
        
        elif detected_type == 'boolean':
            return self._convert_to_boolean(series)
        
        elif detected_type == 'datetime':
            return pd.to_datetime(series, errors='coerce')
        
        elif detected_type == 'date':
            return pd.to_datetime(series, errors='coerce').dt.date
        
        else:  # string
            # Convert to string and handle nulls
            return series.astype(str).replace('nan', None)
    
    def _convert_to_boolean(self, series: pd.Series) -> pd.Series:
        """
        Convert series to boolean values.
        
        Args:
            series: Input series
            
        Returns:
            Boolean series
        """
        # Define true/false mappings
        true_values = {'true', '1', 'yes', 'y', 't', 'on'}
        false_values = {'false', '0', 'no', 'n', 'f', 'off'}
        
        def convert_value(val):
            if pd.isna(val):
                return None
            str_val = str(val).lower().strip()
            if str_val in true_values:
                return True
            elif str_val in false_values:
                return False
            else:
                return None
        
        return series.apply(convert_value)
    
    def _insert_batches(self, df: pd.DataFrame, table_name: str, skip_errors: bool) -> Dict[str, Any]:
        """
        Insert data in batches for better performance.
        
        Args:
            df: Transformed DataFrame
            table_name: Target table name
            skip_errors: Whether to skip rows with errors
            
        Returns:
            Statistics dictionary
        """
        total_rows = len(df)
        successful_rows = 0
        failed_rows = 0
        error_details = []
        
        # Clean table name
        clean_table_name = self._clean_identifier(table_name)
        
        # Prepare column names for SQL
        columns = [f'"{col}"' for col in df.columns]
        placeholders = ', '.join(['%s'] * len(columns))
        
        insert_sql = f"""
        INSERT INTO "{clean_table_name}" ({', '.join(columns)})
        VALUES ({placeholders})
        """
        
        # Process in batches
        for start_idx in range(0, total_rows, self.batch_size):
            end_idx = min(start_idx + self.batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            logger.info(f"Processing batch {start_idx + 1}-{end_idx} of {total_rows}")
            
            # Convert batch to list of tuples
            batch_data = []
            for _, row in batch_df.iterrows():
                try:
                    # Convert row to tuple, handling NaN values
                    row_tuple = tuple(None if pd.isna(val) else val for val in row)
                    batch_data.append(row_tuple)
                except Exception as e:
                    if skip_errors:
                        failed_rows += 1
                        error_details.append(f"Row {start_idx + len(batch_data)}: {e}")
                        continue
                    else:
                        raise
            
            # Insert batch
            if batch_data:
                try:
                    self.db.cursor.executemany(insert_sql, batch_data)
                    self.db.connection.commit()
                    successful_rows += len(batch_data)
                    logger.info(f"Successfully inserted {len(batch_data)} rows")
                    
                except Exception as e:
                    if skip_errors:
                        failed_rows += len(batch_data)
                        error_details.append(f"Batch {start_idx}-{end_idx}: {e}")
                        self.db.connection.rollback()
                        logger.warning(f"Failed to insert batch: {e}")
                    else:
                        self.db.connection.rollback()
                        raise
        
        # Compile statistics
        stats = {
            'total_rows': total_rows,
            'successful_rows': successful_rows,
            'failed_rows': failed_rows,
            'success_rate': (successful_rows / total_rows) * 100 if total_rows > 0 else 0,
            'error_details': error_details[:10]  # Limit error details
        }
        
        logger.info(f"Insertion complete: {successful_rows}/{total_rows} rows successful "
                   f"({stats['success_rate']:.1f}%)")
        
        return stats
    
    def _clean_identifier(self, identifier: str) -> str:
        """
        Clean database identifier to be PostgreSQL compliant.
        
        Args:
            identifier: Original identifier
            
        Returns:
            Cleaned identifier
        """
        # Convert to lowercase and replace non-alphanumeric chars with underscores
        clean = re.sub(r'[^\w]', '_', identifier.lower())
        
        # Ensure it starts with a letter or underscore
        if clean and not clean[0].isalpha() and clean[0] != '_':
            clean = f'col_{clean}'
        
        # Ensure it's not empty
        if not clean:
            clean = 'unnamed_column'
        
        # Truncate if too long
        if len(clean) > 63:
            clean = clean[:63]
        
        return clean
    
    def validate_data_integrity(self, table_name: str, expected_count: int) -> bool:
        """
        Validate that data was inserted correctly.
        
        Args:
            table_name: Name of the table
            expected_count: Expected number of rows
            
        Returns:
            bool: True if validation passes, False otherwise
        """
        try:
            clean_table_name = self._clean_identifier(table_name)
            count_query = f'SELECT COUNT(*) as count FROM "{clean_table_name}";'
            result = self.db.execute_query(count_query)
            
            if result:
                actual_count = result[0]['count']
                logger.info(f"Data validation: Expected {expected_count}, Found {actual_count}")
                return actual_count == expected_count
            
            return False
            
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            return False
