"""
Example usage of the CSV to PostgreSQL automation tool.
Demonstrates different ways to use the CSVToDatabase class.
"""

from csv_to_db import CSVToDatabase
import logging

# Configure logging to see what's happening
logging.basicConfig(level=logging.INFO)

def example_basic_usage():
    """Basic example: Process a CSV file with default settings."""
    print("=== Basic Usage Example ===")
    
    # Initialize the processor
    processor = CSVToDatabase()
    
    # Process the example CSV file
    results = processor.process_csv(
        csv_file_path='example_data.csv',
        table_name='employees'
    )
    
    if results['success']:
        print(f"✅ Success! Processed {results['data_insertion']['successful_rows']} rows")
        print(f"⏱️  Duration: {results['total_duration']}")
    else:
        print(f"❌ Failed: {results['error']}")
    
    return results

def example_with_custom_settings():
    """Example with custom settings and error handling."""
    print("\n=== Custom Settings Example ===")
    
    # Custom database configuration (optional - can use .env instead)
    db_config = {
        'host': 'localhost',
        'port': '5432',
        'database': 'your_database',
        'user': 'your_username',
        'password': 'your_password'
    }
    
    # Initialize with custom settings
    processor = CSVToDatabase(
        db_config=db_config,  # Optional: use None to load from .env
        sample_size=500,      # Analyze only 500 rows for type detection
        batch_size=500        # Insert 500 rows per batch
    )
    
    # Process with custom options
    results = processor.process_csv(
        csv_file_path='example_data.csv',
        table_name='employees_custom',
        delimiter=',',
        encoding='utf-8',
        drop_if_exists=True,    # Drop table if it exists
        skip_errors=True,       # Skip problematic rows
        create_indexes=True     # Create indexes on appropriate columns
    )
    
    print(f"Results: {results}")
    return results

def example_analyze_only():
    """Example: Only analyze CSV without creating table or inserting data."""
    print("\n=== Analysis Only Example ===")
    
    processor = CSVToDatabase()
    
    # Analyze the CSV file structure
    analysis = processor.analyze_csv_only('example_data.csv')
    
    print(f"File has {analysis['total_rows']} rows and {analysis['total_columns']} columns")
    print("\nDetected column types:")
    for column_name, column_info in analysis['columns'].items():
        print(f"  {column_name}: {column_info['postgresql_type']} "
              f"(nullable: {column_info['nullable']})")
    
    return analysis

def example_generate_sql():
    """Example: Generate CREATE TABLE SQL without executing it."""
    print("\n=== Generate SQL Example ===")
    
    processor = CSVToDatabase()
    
    # Generate CREATE TABLE SQL
    sql = processor.generate_create_table_sql('example_data.csv', 'employees_sql_example')
    
    print("Generated SQL:")
    print(sql)
    
    return sql

def example_error_handling():
    """Example: Demonstrate error handling."""
    print("\n=== Error Handling Example ===")
    
    processor = CSVToDatabase()
    
    # Try to process a non-existent file
    results = processor.process_csv(
        csv_file_path='non_existent_file.csv',
        table_name='test_table'
    )
    
    if not results['success']:
        print(f"Expected error: {results['error']}")
    
    return results

if __name__ == "__main__":
    print("CSV to PostgreSQL Automation Tool - Examples")
    print("=" * 50)
    
    # Run examples
    try:
        # 1. Basic usage
        example_basic_usage()
        
        # 2. Custom settings
        example_with_custom_settings()
        
        # 3. Analysis only
        example_analyze_only()
        
        # 4. Generate SQL
        example_generate_sql()
        
        # 5. Error handling
        example_error_handling()
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("Make sure you have:")
        print("1. Created a .env file with your database credentials")
        print("2. Installed required packages: pip install -r requirements.txt")
        print("3. Have a PostgreSQL database running and accessible")
