"""
Specialized configuration and utilities for multilingual terminology databases.
Optimized for CSV files with language columns and normalized versions.
"""

from csv_to_db import CSVToDatabase
from typing import Dict, List, Any, Optional
import re


class MultilingualCSVProcessor(CSVToDatabase):
    """
    Specialized processor for multilingual terminology CSV files.
    Optimized for handling language columns and creating appropriate indexes.
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None, 
                 sample_size: int = 1000, batch_size: int = 1000):
        """
        Initialize multilingual CSV processor.
        
        Args:
            db_config: Optional database configuration dict
            sample_size: Number of rows to sample for analysis
            batch_size: Number of rows to insert per batch
        """
        super().__init__(db_config, sample_size, batch_size)
        
        # Language codes commonly used
        self.language_codes = {
            'en': 'English',
            'es': 'Spanish', 
            'fr': 'French',
            'ar': 'Arabic',
            'de': 'German',
            'uk': 'Ukrainian',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ru': 'Russian',
            'pt': 'Portuguese',
            'it': 'Italian'
        }
    
    def analyze_multilingual_structure(self, csv_file_path: str, 
                                     delimiter: str = ',', encoding: str = 'utf-8') -> Dict[str, Any]:
        """
        Analyze CSV with focus on multilingual structure.
        
        Args:
            csv_file_path: Path to CSV file
            delimiter: CSV delimiter
            encoding: File encoding
            
        Returns:
            Enhanced analysis with multilingual insights
        """
        # Get basic analysis
        analysis = self.analyze_csv_only(csv_file_path, delimiter, encoding)
        
        # Add multilingual-specific analysis
        multilingual_info = self._analyze_multilingual_patterns(analysis)
        analysis['multilingual_info'] = multilingual_info
        
        return analysis
    
    def _analyze_multilingual_patterns(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze patterns specific to multilingual data.
        
        Args:
            analysis: Basic CSV analysis results
            
        Returns:
            Multilingual-specific insights
        """
        columns = analysis['columns']
        
        # Identify language columns
        language_columns = []
        normalized_columns = []
        code_columns = []
        
        for col_name in columns.keys():
            col_lower = col_name.lower()
            
            # Check for language code columns
            if col_lower in self.language_codes:
                language_columns.append({
                    'column': col_name,
                    'language': self.language_codes[col_lower],
                    'type': 'original'
                })
            
            # Check for normalized columns
            elif '_normalized' in col_lower or '_norm' in col_lower:
                # Extract language code
                lang_code = col_lower.replace('_normalized', '').replace('_norm', '')
                if lang_code in self.language_codes:
                    normalized_columns.append({
                        'column': col_name,
                        'language': self.language_codes[lang_code],
                        'type': 'normalized'
                    })
            
            # Check for code columns
            elif 'code' in col_lower:
                code_columns.append(col_name)
        
        # Identify potential primary keys
        primary_key_candidates = []
        for col_name, col_info in columns.items():
            if (col_info.get('is_unique', False) and 
                not col_info.get('nullable', True) and
                'code' in col_name.lower()):
                primary_key_candidates.append(col_name)
        
        # Suggest optimal data types for multilingual content
        suggestions = self._generate_multilingual_suggestions(columns)
        
        return {
            'language_columns': language_columns,
            'normalized_columns': normalized_columns,
            'code_columns': code_columns,
            'primary_key_candidates': primary_key_candidates,
            'total_languages': len(language_columns),
            'has_normalized_versions': len(normalized_columns) > 0,
            'optimization_suggestions': suggestions
        }
    
    def _generate_multilingual_suggestions(self, columns: Dict[str, Any]) -> List[str]:
        """
        Generate optimization suggestions for multilingual data.
        
        Args:
            columns: Column analysis information
            
        Returns:
            List of optimization suggestions
        """
        suggestions = []
        
        # Check for text length optimization
        long_text_columns = []
        for col_name, col_info in columns.items():
            if 'VARCHAR' in col_info.get('postgresql_type', ''):
                # Extract length from VARCHAR(n)
                match = re.search(r'VARCHAR\((\d+)\)', col_info['postgresql_type'])
                if match and int(match.group(1)) > 100:
                    long_text_columns.append(col_name)
        
        if long_text_columns:
            suggestions.append(f"Consider using TEXT type for long content columns: {', '.join(long_text_columns)}")
        
        # Suggest indexes for search
        text_columns = [col for col, info in columns.items() 
                       if 'TEXT' in info.get('postgresql_type', '') or 'VARCHAR' in info.get('postgresql_type', '')]
        
        if text_columns:
            suggestions.append("Consider adding GIN indexes for full-text search on language columns")
            suggestions.append("Consider adding trigram indexes (pg_trgm) for fuzzy text matching")
        
        # Suggest normalization
        normalized_cols = [col for col in columns.keys() if 'normalized' in col.lower()]
        original_cols = [col for col in columns.keys() if col.lower() in self.language_codes]
        
        if len(original_cols) > len(normalized_cols):
            suggestions.append("Consider adding normalized versions for all language columns for better search performance")
        
        return suggestions
    
    def create_multilingual_table(self, csv_file_path: str, table_name: str,
                                delimiter: str = ',', encoding: str = 'utf-8',
                                drop_if_exists: bool = False,
                                add_search_indexes: bool = True) -> Dict[str, Any]:
        """
        Create table optimized for multilingual data.
        
        Args:
            csv_file_path: Path to CSV file
            table_name: Name for the database table
            delimiter: CSV delimiter
            encoding: File encoding
            drop_if_exists: Whether to drop existing table
            add_search_indexes: Whether to add search-optimized indexes
            
        Returns:
            Process results with multilingual optimizations
        """
        # Process with standard method
        results = self.process_csv(
            csv_file_path=csv_file_path,
            table_name=table_name,
            delimiter=delimiter,
            encoding=encoding,
            drop_if_exists=drop_if_exists,
            skip_errors=False,
            create_indexes=True
        )
        
        # Add multilingual-specific optimizations if successful
        if results['success'] and add_search_indexes:
            self._add_multilingual_indexes(table_name, csv_file_path, delimiter, encoding)
        
        return results
    
    def _add_multilingual_indexes(self, table_name: str, csv_file_path: str,
                                delimiter: str, encoding: str):
        """
        Add indexes optimized for multilingual search.
        
        Args:
            table_name: Name of the table
            csv_file_path: Path to original CSV file
            delimiter: CSV delimiter
            encoding: File encoding
        """
        try:
            # Analyze structure to identify language columns
            analysis = self.analyze_multilingual_structure(csv_file_path, delimiter, encoding)
            multilingual_info = analysis['multilingual_info']
            
            # Add indexes for language columns
            language_columns = [col['column'] for col in multilingual_info['language_columns']]
            normalized_columns = [col['column'] for col in multilingual_info['normalized_columns']]
            
            # Create indexes for search performance
            for col in language_columns + normalized_columns:
                clean_col = self._clean_identifier(col)
                clean_table = self._clean_identifier(table_name)
                
                # Regular index for exact matches
                index_name = f"idx_{clean_table}_{clean_col}"
                index_sql = f'CREATE INDEX IF NOT EXISTS "{index_name}" ON "{clean_table}" ("{clean_col}");'
                self.db_connection.execute_command(index_sql)
                
                # Trigram index for fuzzy matching (if pg_trgm extension is available)
                trigram_index_name = f"idx_{clean_table}_{clean_col}_trgm"
                trigram_sql = f'CREATE INDEX IF NOT EXISTS "{trigram_index_name}" ON "{clean_table}" USING gin ("{clean_col}" gin_trgm_ops);'
                # This will fail silently if pg_trgm is not installed
                self.db_connection.execute_command(trigram_sql)
            
        except Exception as e:
            print(f"Warning: Could not add multilingual indexes: {e}")
    
    def _clean_identifier(self, identifier: str) -> str:
        """Clean database identifier to be PostgreSQL compliant."""
        clean = re.sub(r'[^\w]', '_', identifier.lower())
        if clean and not clean[0].isalpha() and clean[0] != '_':
            clean = f'col_{clean}'
        if not clean:
            clean = 'unnamed_column'
        if len(clean) > 63:
            clean = clean[:63]
        return clean


def main():
    """Example usage of multilingual processor."""
    print("=== Multilingual CSV Processor Example ===")
    
    # Initialize processor
    processor = MultilingualCSVProcessor()
    
    # Analyze multilingual structure
    analysis = processor.analyze_multilingual_structure('multilingual_terms_example.csv')
    
    print("Multilingual Analysis:")
    multilingual_info = analysis['multilingual_info']
    
    print(f"Languages detected: {multilingual_info['total_languages']}")
    print(f"Has normalized versions: {multilingual_info['has_normalized_versions']}")
    print(f"Primary key candidates: {multilingual_info['primary_key_candidates']}")
    
    print("\nLanguage columns:")
    for lang_col in multilingual_info['language_columns']:
        print(f"  - {lang_col['column']} ({lang_col['language']})")
    
    print("\nNormalized columns:")
    for norm_col in multilingual_info['normalized_columns']:
        print(f"  - {norm_col['column']} ({norm_col['language']})")
    
    print("\nOptimization suggestions:")
    for suggestion in multilingual_info['optimization_suggestions']:
        print(f"  • {suggestion}")


if __name__ == "__main__":
    main()
