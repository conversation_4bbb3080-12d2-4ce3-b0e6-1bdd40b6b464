# Quick Start Guide: Test Your CSV Migration

## 🚀 Step-by-Step Testing Process

### Step 1: Set Up Database Connection

1. **Copy the environment template:**
   ```bash
   copy .env.example .env
   ```

2. **Edit `.env` file with your PostgreSQL credentials:**
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=icd11_db
   DB_USER=your_username
   DB_PASSWORD=your_password
   ```

3. **Make sure PostgreSQL is running** on your system

### Step 2: Run the Complete Test

```bash
py test_migration.py
```

This single command will:
- ✅ Test database connection
- ✅ Verify your CSV file exists
- ✅ Create the future-proof schema
- ✅ Migrate all your data
- ✅ Verify data integrity
- ✅ Test search functionality

## 🔍 What to Expect

### Successful Output Should Look Like:
```
🧪 ICD-11 CSV to Database Migration Test
==================================================
=== Testing Database Setup ===
✅ Database connection successful!
✅ PostgreSQL version: PostgreSQL 15.x...

=== Testing CSV File ===
✅ CSV file loaded successfully!
   📊 Rows: 324
   📊 Columns: 14

=== Running Migration Test ===
🚀 Starting migration...
Creating future-proof schema...
Schema created successfully!
Loading CSV file: icd11_multilingual_terms_normalized_6lang...
Loaded 324 rows
Processing chapters...
Processing terms...
Processing translations...
Processing normalizations...
✅ Migration completed successfully!
   📊 Records processed: 324
   📊 Chapters created: X
   📊 Terms created: 324
   📊 Translations created: 1944
   📊 Normalizations created: 1944

=== Verifying Data Integrity ===
✅ Languages table: 6 records
✅ Chapters table: X records
✅ Terms table: 324 records
✅ Translations table: 1944 records
✅ Normalizations table: 1944 records
✅ Complete view: 1944 records

📋 Sample Data Check:
✅ Sample translations:
   TECH001.TERM001 (en): Computer system...
   TECH001.TERM001 (es): Sistema informático...

=== Testing Search Functionality ===
✅ Find specific term: 1 results
✅ Search by text: 3 results
✅ Language-specific search: 324 results
✅ Chapter search: X results

==================================================
🎉 MIGRATION TEST COMPLETED SUCCESSFULLY!
==================================================
```

## 🛠️ Troubleshooting

### Common Issues:

#### ❌ Database Connection Failed
**Problem:** Cannot connect to PostgreSQL
**Solutions:**
- Check if PostgreSQL is running: `pg_ctl status`
- Verify credentials in `.env` file
- Ensure database exists: `createdb icd11_db`
- Check if PostgreSQL service is started

#### ❌ CSV File Not Found
**Problem:** Cannot find your CSV file
**Solutions:**
- Make sure the CSV file is in the project directory
- Check the exact filename (it should start with 'icd11')
- Verify file permissions

#### ❌ Migration Errors
**Problem:** Data migration fails
**Solutions:**
- Check database permissions
- Ensure PostgreSQL version supports required features
- Verify CSV data format

## 🔍 Manual Verification Commands

If you want to check specific aspects manually:

### Check Database Tables:
```bash
py -c "
from database_connection import DatabaseConnection
db = DatabaseConnection()
db.connect()
tables = db.execute_query(\"SELECT tablename FROM pg_tables WHERE schemaname='public';\")
print('Tables created:', [t[0] for t in tables])
db.disconnect()
"
```

### Count Your Data:
```bash
py -c "
from database_connection import DatabaseConnection
db = DatabaseConnection()
db.connect()
count = db.execute_query('SELECT COUNT(*) FROM v_terms_complete;')[0][0]
print(f'Total term-language combinations: {count}')
db.disconnect()
"
```

### Sample Your Data:
```bash
py -c "
from database_connection import DatabaseConnection
db = DatabaseConnection()
db.connect()
sample = db.execute_query('SELECT chapter_code, term_code, language_code, translation_text FROM v_terms_primary LIMIT 5;')
for row in sample:
    print(f'{row[0]}.{row[1]} ({row[2]}): {row[3][:50]}...')
db.disconnect()
"
```

### Search Your Data:
```bash
py -c "
from database_connection import DatabaseConnection
db = DatabaseConnection()
db.connect()
results = db.execute_query(\"SELECT * FROM v_terms_primary WHERE translation_text ILIKE '%computer%' LIMIT 3;\")
print(f'Found {len(results)} terms containing \"computer\"')
for row in results:
    print(f'  {row[0]}.{row[1]} ({row[2]}): {row[3]}')
db.disconnect()
"
```

## 📊 Understanding Your Results

After successful migration, you'll have:

- **6 Languages**: en, es, fr, ar, de, uk
- **324 Terms**: Your original CSV rows
- **~1,944 Translations**: 324 terms × 6 languages
- **~1,944 Normalizations**: Optimized search versions
- **Performance Indexes**: For fast searching
- **Convenience Views**: For easy querying

## 🎯 Next Steps

Once migration is successful:

1. **Explore your data** using the sample queries above
2. **Read the documentation** in `FUTURE_PROOF_SCHEMA.md`
3. **Try advanced searches** with the new schema capabilities
4. **Consider adding more languages** or data sources

## 🆘 Need Help?

If you encounter issues:
1. Run `py test_migration.py` and share the output
2. Check the error messages for specific guidance
3. Verify your PostgreSQL installation and credentials
4. Ensure all required Python packages are installed: `pip install -r requirements.txt`
