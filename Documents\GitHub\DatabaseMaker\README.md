# CSV to PostgreSQL Database Automation

This tool automates the process of reading CSV files and inserting their data into PostgreSQL databases with intelligent type detection and table creation.

## Features

- **Automatic CSV Analysis**: Detects column types, nullable fields, and data patterns
- **Dynamic Table Creation**: Creates PostgreSQL tables with appropriate data types
- **Intelligent Type Mapping**: Maps CSV data to optimal PostgreSQL types (INTEGER, NUMERIC, BOOLEAN, TIMESTAMP, DATE, TEXT, VARCHAR)
- **Efficient Bulk Loading**: Processes large files in configurable batches
- **Error Handling**: Robust error handling with optional error skipping
- **Index Creation**: Automatically creates indexes on appropriate columns
- **Data Validation**: Verifies data integrity after insertion
- **Flexible Configuration**: Environment-based or programmatic configuration

## Quick Start

1. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

2. **Configure database connection:**

   ```bash
   cp .env.example .env
   # Edit .env with your PostgreSQL credentials
   ```

3. **Run with example data:**
   ```bash
   python csv_to_db.py example_data.csv employees
   ```

## Usage Examples

### Command Line Interface

```bash
# Basic usage
python csv_to_db.py data.csv my_table

# With options
python csv_to_db.py data.csv my_table --drop-if-exists --skip-errors

# Analyze only (no database operations)
python csv_to_db.py data.csv my_table --analyze-only

# Generate SQL only
python csv_to_db.py data.csv my_table --generate-sql
```

### Python API

```python
from csv_to_db import CSVToDatabase

# Basic usage
processor = CSVToDatabase()
results = processor.process_csv('data.csv', 'my_table')

# With custom settings
processor = CSVToDatabase(sample_size=500, batch_size=1000)
results = processor.process_csv(
    csv_file_path='data.csv',
    table_name='my_table',
    drop_if_exists=True,
    skip_errors=True
)

# Analysis only
analysis = processor.analyze_csv_only('data.csv')
print(f"Detected {analysis['total_columns']} columns")
```

## Configuration

### Environment Variables (.env file)

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database
DB_USER=your_username
DB_PASSWORD=your_password
DB_SSLMODE=prefer
```

### Programmatic Configuration

```python
db_config = {
    'host': 'localhost',
    'port': '5432',
    'database': 'mydb',
    'user': 'myuser',
    'password': 'mypass'
}
processor = CSVToDatabase(db_config=db_config)
```

## Data Type Detection

The tool automatically detects and maps data types:

| Detected Type  | PostgreSQL Type | Example Values             |
| -------------- | --------------- | -------------------------- |
| Integer        | INTEGER         | 1, 42, -100                |
| Float          | NUMERIC         | 3.14, -2.5, 1.0            |
| Boolean        | BOOLEAN         | true, false, 1, 0, yes, no |
| Date           | DATE            | 2023-01-15, 01/15/2023     |
| DateTime       | TIMESTAMP       | 2023-01-15 10:30:00        |
| String (short) | VARCHAR(n)      | Text up to 255 chars       |
| String (long)  | TEXT            | Longer text content        |

## Error Handling

- **Connection Errors**: Validates database connectivity before processing
- **File Errors**: Handles missing files, encoding issues, malformed CSV
- **Data Errors**: Optional error skipping for problematic rows
- **Type Conversion**: Graceful handling of type conversion failures
- **Constraint Violations**: Detailed error reporting for database constraints

## Performance Features

- **Batch Processing**: Configurable batch sizes for memory efficiency
- **Sampling**: Analyzes subset of data for faster type detection on large files
- **Bulk Insert**: Uses efficient PostgreSQL bulk insert operations
- **Index Creation**: Creates indexes on unique/frequently queried columns

## Requirements

- Python 3.8+
- PostgreSQL 10+
- Required packages: pandas, psycopg2-binary, python-dotenv, sqlalchemy, numpy

## Files Structure

```
DatabaseMaker/
├── csv_to_db.py           # Main automation script
├── database_connection.py # Database connection management
├── csv_analyzer.py        # CSV analysis and type detection
├── table_creator.py       # Table creation logic
├── data_inserter.py       # Data insertion operations
├── example_usage.py       # Usage examples
├── example_data.csv       # Sample data file
├── requirements.txt       # Python dependencies
├── .env.example          # Environment configuration template
└── README.md             # This file
```
