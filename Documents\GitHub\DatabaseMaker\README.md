# CSV to PostgreSQL Database Automation

This tool automates the process of reading CSV files and inserting their data into PostgreSQL databases.

## Features

- Automatic CSV structure analysis
- Dynamic table creation based on CSV columns
- Efficient bulk data insertion
- Data type detection and mapping
- Error handling and validation
- Configuration-based setup

## Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Copy `.env.example` to `.env` and configure your database settings:
   ```bash
   cp .env.example .env
   ```

3. Edit `.env` with your PostgreSQL connection details.

## Usage

```python
from csv_to_db import CSVToDatabase

# Initialize the processor
processor = CSVToDatabase()

# Process a CSV file
processor.process_csv('path/to/your/file.csv', 'table_name')
```

## Requirements

- Python 3.8+
- PostgreSQL database
- Required Python packages (see requirements.txt)
