"""
Table creation module for PostgreSQL database operations.
Handles automatic table creation based on CSV analysis results.
"""

import logging
import re
from typing import Dict, List, Any, Optional
from database_connection import DatabaseConnection

logger = logging.getLogger(__name__)


class TableCreator:
    """Creates PostgreSQL tables based on CSV analysis results."""
    
    def __init__(self, db_connection: DatabaseConnection):
        """
        Initialize table creator with database connection.
        
        Args:
            db_connection: Active database connection instance
        """
        self.db = db_connection
    
    def create_table_from_analysis(self, analysis: Dict[str, Any], table_name: str, 
                                 drop_if_exists: bool = False) -> bool:
        """
        Create a PostgreSQL table based on CSV analysis results.
        
        Args:
            analysis: Analysis results from CSVAnalyzer
            table_name: Name for the new table
            drop_if_exists: Whether to drop existing table before creating
            
        Returns:
            bool: True if table created successfully, False otherwise
        """
        try:
            # Clean table name
            clean_table_name = self._clean_identifier(table_name)
            
            # Check if table exists
            if self.db.table_exists(clean_table_name):
                if drop_if_exists:
                    logger.info(f"Dropping existing table: {clean_table_name}")
                    if not self._drop_table(clean_table_name):
                        return False
                else:
                    logger.warning(f"Table {clean_table_name} already exists. Use drop_if_exists=True to replace it.")
                    return False
            
            # Generate CREATE TABLE SQL
            create_sql = self._generate_create_table_sql(analysis, clean_table_name)
            logger.info(f"Creating table with SQL:\n{create_sql}")
            
            # Execute CREATE TABLE
            success = self.db.execute_command(create_sql)
            
            if success:
                logger.info(f"Successfully created table: {clean_table_name}")
                
                # Log table structure
                self._log_table_structure(clean_table_name)
                
            return success
            
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
            return False
    
    def _generate_create_table_sql(self, analysis: Dict[str, Any], table_name: str) -> str:
        """
        Generate CREATE TABLE SQL statement based on analysis.
        
        Args:
            analysis: Analysis results from CSVAnalyzer
            table_name: Clean table name
            
        Returns:
            SQL CREATE TABLE statement
        """
        columns = []
        primary_key_candidates = []
        
        for column_name, column_info in analysis['columns'].items():
            # Clean column name
            clean_name = self._clean_identifier(column_name)
            
            # Build column definition
            column_def = self._build_column_definition(clean_name, column_info)
            columns.append(column_def)
            
            # Track potential primary key candidates
            if column_info.get('is_unique') and not column_info.get('nullable'):
                primary_key_candidates.append(clean_name)
        
        # Build the CREATE TABLE statement
        sql_parts = [f'CREATE TABLE "{table_name}" (']
        
        # Add column definitions
        sql_parts.extend([f'    {col},' for col in columns])
        
        # Add primary key if we found a good candidate
        if primary_key_candidates:
            # Use the first unique, non-nullable column as primary key
            pk_column = primary_key_candidates[0]
            sql_parts.append(f'    PRIMARY KEY ("{pk_column}")')
        else:
            # Remove trailing comma from last column
            sql_parts[-1] = sql_parts[-1].rstrip(',')
        
        sql_parts.append(');')
        
        return '\n'.join(sql_parts)
    
    def _build_column_definition(self, column_name: str, column_info: Dict[str, Any]) -> str:
        """
        Build column definition for CREATE TABLE statement.
        
        Args:
            column_name: Clean column name
            column_info: Column analysis information
            
        Returns:
            Column definition string
        """
        # Start with column name and type
        definition = f'"{column_name}" {column_info["postgresql_type"]}'
        
        # Add constraints
        if not column_info.get('nullable', True):
            definition += ' NOT NULL'
        
        # Add default value for boolean columns if appropriate
        if column_info['postgresql_type'] == 'BOOLEAN' and not column_info.get('nullable', True):
            definition += ' DEFAULT FALSE'
        
        return definition
    
    def _clean_identifier(self, identifier: str) -> str:
        """
        Clean database identifier (table/column name) to be PostgreSQL compliant.
        
        Args:
            identifier: Original identifier
            
        Returns:
            Cleaned identifier
        """
        # Convert to lowercase and replace non-alphanumeric chars with underscores
        clean = re.sub(r'[^\w]', '_', identifier.lower())
        
        # Ensure it starts with a letter or underscore
        if clean and not clean[0].isalpha() and clean[0] != '_':
            clean = f'col_{clean}'
        
        # Ensure it's not empty
        if not clean:
            clean = 'unnamed_column'
        
        # Truncate if too long (PostgreSQL limit is 63 characters)
        if len(clean) > 63:
            clean = clean[:63]
        
        return clean
    
    def _drop_table(self, table_name: str) -> bool:
        """
        Drop a table if it exists.
        
        Args:
            table_name: Name of table to drop
            
        Returns:
            bool: True if successful, False otherwise
        """
        drop_sql = f'DROP TABLE IF EXISTS "{table_name}" CASCADE;'
        return self.db.execute_command(drop_sql)
    
    def _log_table_structure(self, table_name: str):
        """
        Log the structure of the created table.
        
        Args:
            table_name: Name of the table
        """
        try:
            columns = self.db.get_table_columns(table_name)
            if columns:
                logger.info(f"Table '{table_name}' structure:")
                for col in columns:
                    nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                    default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                    logger.info(f"  {col['column_name']}: {col['data_type']} {nullable}{default}")
        except Exception as e:
            logger.warning(f"Could not retrieve table structure: {e}")
    
    def add_indexes(self, table_name: str, analysis: Dict[str, Any]) -> bool:
        """
        Add appropriate indexes to the table based on analysis.
        
        Args:
            table_name: Name of the table
            analysis: Analysis results from CSVAnalyzer
            
        Returns:
            bool: True if indexes added successfully, False otherwise
        """
        try:
            success = True
            
            for column_name, column_info in analysis['columns'].items():
                clean_column = self._clean_identifier(column_name)
                
                # Add index for unique columns that aren't primary keys
                if (column_info.get('is_unique') and 
                    column_info['postgresql_type'] not in ['TEXT'] and
                    column_info.get('null_percentage', 0) < 10):
                    
                    index_name = f"idx_{table_name}_{clean_column}"
                    index_sql = f'CREATE INDEX IF NOT EXISTS "{index_name}" ON "{table_name}" ("{clean_column}");'
                    
                    if self.db.execute_command(index_sql):
                        logger.info(f"Created index: {index_name}")
                    else:
                        success = False
                        logger.warning(f"Failed to create index: {index_name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to add indexes: {e}")
            return False
