#!/usr/bin/env python3
"""
Migration script to populate the future-proof schema with CSV data.
Transforms flat CSV structure into normalized relational structure.
"""

import pandas as pd
from future_proof_schema import FutureProofSchemaGenerator
from database_connection import DatabaseConnection
from typing import Dict, List, Optional, Tuple
import logging
import hashlib

class CSVToSchemaConverter:
    """
    Converts flat CSV data to normalized future-proof schema.
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize converter with database connection."""
        self.db_connection = DatabaseConnection(db_config)
        self.logger = logging.getLogger(__name__)
        
        # Language mapping from CSV columns to language codes
        self.language_mapping = {
            'en': 'en',
            'es': 'es', 
            'fr': 'fr',
            'ar': 'ar',
            'de': 'de',
            'uk': 'uk'
        }
        
        # Normalized column mapping
        self.normalized_mapping = {
            'en_normalized': 'en',
            'es_normalized': 'es',
            'fr_normalized': 'fr',
            'ar_normalized': 'ar',
            'de_normalized': 'de',
            'uk_normalized': 'uk'
        }
    
    def migrate_csv_to_schema(self, csv_file_path: str, 
                            delimiter: str = ',', encoding: str = 'utf-8',
                            create_schema: bool = True) -> Dict[str, any]:
        """
        Complete migration from CSV to future-proof schema.
        
        Args:
            csv_file_path: Path to CSV file
            delimiter: CSV delimiter
            encoding: File encoding
            create_schema: Whether to create schema first
            
        Returns:
            Migration results
        """
        results = {
            'success': False,
            'records_processed': 0,
            'chapters_created': 0,
            'terms_created': 0,
            'translations_created': 0,
            'normalizations_created': 0,
            'errors': []
        }
        
        try:
            # Create schema if requested
            if create_schema:
                print("Creating future-proof schema...")
                generator = FutureProofSchemaGenerator(self.db_connection.config)
                schema_results = generator.create_future_proof_schema(drop_if_exists=True)
                print("Schema created successfully!")
            
            # Load and validate CSV
            print(f"Loading CSV file: {csv_file_path}")
            df = pd.read_csv(csv_file_path, delimiter=delimiter, encoding=encoding)
            print(f"Loaded {len(df)} rows")
            
            # Validate CSV structure
            self._validate_csv_structure(df)
            
            # Get language IDs
            language_ids = self._get_language_ids()
            
            # Process data in steps
            print("Processing chapters...")
            results['chapters_created'] = self._process_chapters(df)
            
            print("Processing terms...")
            results['terms_created'] = self._process_terms(df)
            
            print("Processing translations...")
            results['translations_created'] = self._process_translations(df, language_ids)
            
            print("Processing normalizations...")
            results['normalizations_created'] = self._process_normalizations(df, language_ids)
            
            # Record metadata
            self._record_migration_metadata(csv_file_path, len(df))
            
            results['success'] = True
            results['records_processed'] = len(df)
            
            print(f"Migration completed successfully!")
            print(f"  Chapters: {results['chapters_created']}")
            print(f"  Terms: {results['terms_created']}")
            print(f"  Translations: {results['translations_created']}")
            print(f"  Normalizations: {results['normalizations_created']}")
            
        except Exception as e:
            error_msg = f"Migration failed: {e}"
            self.logger.error(error_msg)
            results['errors'].append(error_msg)
            print(error_msg)
        
        return results
    
    def _validate_csv_structure(self, df: pd.DataFrame):
        """Validate that CSV has expected structure."""
        required_columns = ['chapter_code', 'term_code']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for language columns
        language_columns = [col for col in df.columns if col in self.language_mapping]
        if not language_columns:
            raise ValueError("No language columns found in CSV")
        
        print(f"Validated CSV structure: {len(language_columns)} language columns found")
    
    def _get_language_ids(self) -> Dict[str, int]:
        """Get language IDs from database."""
        sql = 'SELECT "language_code", "language_id" FROM "languages" WHERE "is_active" = TRUE;'
        results = self.db_connection.execute_query(sql)
        return {row[0]: row[1] for row in results}
    
    def _process_chapters(self, df: pd.DataFrame) -> int:
        """Process and insert unique chapters."""
        unique_chapters = df['chapter_code'].unique()
        created_count = 0
        
        for chapter_code in unique_chapters:
            if pd.isna(chapter_code):
                continue
                
            sql = """
            INSERT INTO "chapters" ("chapter_code")
            VALUES (%s)
            ON CONFLICT ("chapter_code") DO NOTHING;
            """
            
            try:
                self.db_connection.execute_command(sql, (str(chapter_code),))
                created_count += 1
            except Exception as e:
                self.logger.warning(f"Could not insert chapter {chapter_code}: {e}")
        
        return created_count
    
    def _process_terms(self, df: pd.DataFrame) -> int:
        """Process and insert terms."""
        created_count = 0
        
        for _, row in df.iterrows():
            chapter_code = str(row['chapter_code'])
            term_code = str(row['term_code'])
            
            # Get chapter_id
            chapter_sql = 'SELECT "chapter_id" FROM "chapters" WHERE "chapter_code" = %s;'
            chapter_result = self.db_connection.execute_query(chapter_sql, (chapter_code,))
            
            if not chapter_result:
                self.logger.warning(f"Chapter not found: {chapter_code}")
                continue
            
            chapter_id = chapter_result[0][0]
            
            # Insert term
            term_sql = """
            INSERT INTO "terms" ("term_code", "chapter_id")
            VALUES (%s, %s)
            ON CONFLICT ("term_code", "chapter_id", "term_version") DO NOTHING;
            """
            
            try:
                self.db_connection.execute_command(term_sql, (term_code, chapter_id))
                created_count += 1
            except Exception as e:
                self.logger.warning(f"Could not insert term {term_code}: {e}")
        
        return created_count
    
    def _process_translations(self, df: pd.DataFrame, language_ids: Dict[str, int]) -> int:
        """Process and insert translations."""
        created_count = 0
        
        for _, row in df.iterrows():
            chapter_code = str(row['chapter_code'])
            term_code = str(row['term_code'])
            
            # Get term_id
            term_sql = """
            SELECT t."term_id" 
            FROM "terms" t 
            JOIN "chapters" c ON t."chapter_id" = c."chapter_id"
            WHERE c."chapter_code" = %s AND t."term_code" = %s;
            """
            term_result = self.db_connection.execute_query(term_sql, (chapter_code, term_code))
            
            if not term_result:
                continue
            
            term_id = term_result[0][0]
            
            # Insert translations for each language
            for csv_column, lang_code in self.language_mapping.items():
                if csv_column not in row or pd.isna(row[csv_column]):
                    continue
                
                translation_text = str(row[csv_column]).strip()
                if not translation_text:
                    continue
                
                language_id = language_ids.get(lang_code)
                if not language_id:
                    continue
                
                translation_sql = """
                INSERT INTO "term_translations" ("term_id", "language_id", "translation_text")
                VALUES (%s, %s, %s)
                ON CONFLICT ("term_id", "language_id", "is_primary") DO NOTHING;
                """
                
                try:
                    self.db_connection.execute_command(
                        translation_sql, 
                        (term_id, language_id, translation_text)
                    )
                    created_count += 1
                except Exception as e:
                    self.logger.warning(f"Could not insert translation: {e}")
        
        return created_count
    
    def _process_normalizations(self, df: pd.DataFrame, language_ids: Dict[str, int]) -> int:
        """Process and insert normalizations."""
        created_count = 0
        
        for _, row in df.iterrows():
            chapter_code = str(row['chapter_code'])
            term_code = str(row['term_code'])
            
            # Process normalized columns
            for csv_column, lang_code in self.normalized_mapping.items():
                if csv_column not in row or pd.isna(row[csv_column]):
                    continue
                
                normalized_text = str(row[csv_column]).strip()
                if not normalized_text:
                    continue
                
                language_id = language_ids.get(lang_code)
                if not language_id:
                    continue
                
                # Get translation_id
                translation_sql = """
                SELECT tr."translation_id"
                FROM "term_translations" tr
                JOIN "terms" t ON tr."term_id" = t."term_id"
                JOIN "chapters" c ON t."chapter_id" = c."chapter_id"
                WHERE c."chapter_code" = %s 
                  AND t."term_code" = %s 
                  AND tr."language_id" = %s
                  AND tr."is_primary" = TRUE;
                """
                
                translation_result = self.db_connection.execute_query(
                    translation_sql, 
                    (chapter_code, term_code, language_id)
                )
                
                if not translation_result:
                    continue
                
                translation_id = translation_result[0][0]
                
                # Insert normalization
                normalization_sql = """
                INSERT INTO "term_normalizations" ("translation_id", "normalized_text", "normalization_type")
                VALUES (%s, %s, %s);
                """
                
                try:
                    self.db_connection.execute_command(
                        normalization_sql,
                        (translation_id, normalized_text, 'standard')
                    )
                    created_count += 1
                except Exception as e:
                    self.logger.warning(f"Could not insert normalization: {e}")
        
        return created_count
    
    def _record_migration_metadata(self, csv_file_path: str, record_count: int):
        """Record migration metadata."""
        # Calculate file checksum
        with open(csv_file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        
        metadata_sql = """
        INSERT INTO "schema_metadata" (
            "schema_version", "data_source", "record_count", "checksum", "notes"
        ) VALUES (%s, %s, %s, %s, %s);
        """
        
        try:
            self.db_connection.execute_command(
                metadata_sql,
                ('1.0', csv_file_path, record_count, file_hash, 'Initial CSV migration')
            )
        except Exception as e:
            self.logger.warning(f"Could not record metadata: {e}")


def main():
    """Example usage of CSV to schema converter."""
    print("=== CSV to Future-Proof Schema Migration ===")
    
    # File to migrate
    csv_file = "icd11_multilingual_terms_normalized_6lang - icd11_multilingual_terms_normalized_6lang.csv"
    
    try:
        # Initialize converter
        converter = CSVToSchemaConverter()
        
        # Run migration
        results = converter.migrate_csv_to_schema(csv_file, create_schema=True)
        
        if results['success']:
            print("\n✓ Migration completed successfully!")
            print(f"  Records processed: {results['records_processed']}")
            print(f"  Chapters created: {results['chapters_created']}")
            print(f"  Terms created: {results['terms_created']}")
            print(f"  Translations created: {results['translations_created']}")
            print(f"  Normalizations created: {results['normalizations_created']}")
        else:
            print("\n✗ Migration failed!")
            for error in results['errors']:
                print(f"  Error: {error}")
    
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
