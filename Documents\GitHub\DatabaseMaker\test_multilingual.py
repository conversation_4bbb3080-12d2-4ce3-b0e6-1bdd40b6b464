#!/usr/bin/env python3
"""
Test script for analyzing multilingual CSV structure.
"""

from csv_to_db import CSVToDatabase

def test_multilingual_analysis():
    """Test analysis of multilingual CSV structure."""
    print("=== Testing Multilingual CSV Analysis ===")
    
    try:
        # Test analysis of multilingual CSV
        processor = CSVToDatabase()
        analysis = processor.analyze_csv_only('multilingual_terms_example.csv')
        
        print(f"Total rows: {analysis['total_rows']}")
        print(f"Total columns: {analysis['total_columns']}")
        print()
        
        print("Column Analysis:")
        print("-" * 80)
        print(f"{'Column Name':<25} {'PostgreSQL Type':<20} {'Nullable':<10} {'Unique':<10}")
        print("-" * 80)
        
        for col_name, col_info in analysis['columns'].items():
            nullable = "Yes" if col_info['nullable'] else "No"
            unique = "Yes" if col_info['is_unique'] else "No"
            print(f"{col_name:<25} {col_info['postgresql_type']:<20} {nullable:<10} {unique:<10}")
        
        print("\n=== Generated CREATE TABLE SQL ===")
        sql = processor.generate_create_table_sql('multilingual_terms_example.csv', 'multilingual_terms')
        print(sql)
        
        # Analyze specific characteristics for multilingual data
        print("\n=== Multilingual Data Insights ===")
        
        # Check for potential primary key candidates
        pk_candidates = []
        for col_name, col_info in analysis['columns'].items():
            if col_info['is_unique'] and not col_info['nullable']:
                pk_candidates.append(col_name)
        
        if pk_candidates:
            print(f"Primary key candidates: {', '.join(pk_candidates)}")
        else:
            print("No obvious primary key candidates found")
        
        # Check text columns (likely language content)
        text_columns = []
        for col_name, col_info in analysis['columns'].items():
            if 'TEXT' in col_info['postgresql_type'] or 'VARCHAR' in col_info['postgresql_type']:
                text_columns.append(col_name)
        
        print(f"Text columns (language content): {len(text_columns)}")
        for col in text_columns:
            print(f"  - {col}")
        
        return True
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        return False

if __name__ == "__main__":
    test_multilingual_analysis()
