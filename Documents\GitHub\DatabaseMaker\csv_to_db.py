"""
Main CSV to PostgreSQL automation script.
Orchestrates the entire process of analyzing CSV files and loading them into PostgreSQL.
"""

import os
import logging
import argparse
from typing import Dict, Any, Optional
from datetime import datetime

from database_connection import DatabaseConnection
from csv_analyzer import CSVAnalyzer
from table_creator import TableCreator
from data_inserter import DataInserter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('csv_to_db.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CSVToDatabase:
    """Main class for automating CSV to PostgreSQL database operations."""
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None, 
                 sample_size: int = 1000, batch_size: int = 1000):
        """
        Initialize CSV to Database processor.
        
        Args:
            db_config: Optional database configuration dict
            sample_size: Number of rows to sample for analysis (0 = all rows)
            batch_size: Number of rows to insert per batch
        """
        self.db_config = db_config
        self.sample_size = sample_size
        self.batch_size = batch_size
        
        # Initialize components
        self.db_connection = None
        self.analyzer = CSVAnalyzer(sample_size=sample_size)
        self.table_creator = None
        self.data_inserter = None
    
    def process_csv(self, csv_file_path: str, table_name: str, 
                   delimiter: str = ',', encoding: str = 'utf-8',
                   drop_if_exists: bool = False, skip_errors: bool = False,
                   create_indexes: bool = True) -> Dict[str, Any]:
        """
        Complete process to analyze CSV and load into PostgreSQL.
        
        Args:
            csv_file_path: Path to CSV file
            table_name: Name for the database table
            delimiter: CSV delimiter character
            encoding: File encoding
            drop_if_exists: Whether to drop existing table
            skip_errors: Whether to skip rows with errors during insertion
            create_indexes: Whether to create indexes on appropriate columns
            
        Returns:
            Dictionary with process results and statistics
        """
        start_time = datetime.now()
        results = {
            'success': False,
            'csv_file': csv_file_path,
            'table_name': table_name,
            'start_time': start_time,
            'analysis': None,
            'table_creation': None,
            'data_insertion': None,
            'indexes_created': None,
            'total_duration': None,
            'error': None
        }
        
        try:
            logger.info(f"Starting CSV to Database process for: {csv_file_path}")
            
            # Step 1: Connect to database
            logger.info("Step 1: Connecting to database...")
            if not self._connect_to_database():
                results['error'] = "Failed to connect to database"
                return results
            
            # Step 2: Analyze CSV file
            logger.info("Step 2: Analyzing CSV file...")
            analysis = self.analyzer.analyze_csv(csv_file_path, delimiter, encoding)
            results['analysis'] = {
                'total_rows': analysis['total_rows'],
                'total_columns': analysis['total_columns'],
                'sample_size': analysis['sample_size'],
                'columns': {name: info['postgresql_type'] for name, info in analysis['columns'].items()}
            }
            logger.info(f"Analysis complete: {analysis['total_rows']} rows, {analysis['total_columns']} columns")
            
            # Step 3: Create table
            logger.info("Step 3: Creating database table...")
            table_success = self.table_creator.create_table_from_analysis(
                analysis, table_name, drop_if_exists
            )
            results['table_creation'] = {'success': table_success}
            
            if not table_success:
                results['error'] = "Failed to create database table"
                return results
            
            # Step 4: Insert data
            logger.info("Step 4: Inserting data...")
            insert_success, insert_stats = self.data_inserter.insert_csv_data(
                csv_file_path, table_name, analysis, delimiter, encoding, skip_errors
            )
            results['data_insertion'] = insert_stats
            
            if not insert_success:
                results['error'] = f"Data insertion failed: {insert_stats.get('error', 'Unknown error')}"
                return results
            
            # Step 5: Create indexes (optional)
            if create_indexes:
                logger.info("Step 5: Creating indexes...")
                indexes_success = self.table_creator.add_indexes(table_name, analysis)
                results['indexes_created'] = {'success': indexes_success}
            
            # Step 6: Validate data integrity
            logger.info("Step 6: Validating data integrity...")
            validation_success = self.data_inserter.validate_data_integrity(
                table_name, analysis['total_rows']
            )
            
            if validation_success:
                results['success'] = True
                logger.info("CSV to Database process completed successfully!")
            else:
                results['error'] = "Data validation failed"
                logger.warning("Process completed but data validation failed")
            
        except Exception as e:
            logger.error(f"Process failed with error: {e}")
            results['error'] = str(e)
        
        finally:
            # Calculate total duration
            end_time = datetime.now()
            results['end_time'] = end_time
            results['total_duration'] = str(end_time - start_time)
            
            # Disconnect from database
            if self.db_connection:
                self.db_connection.disconnect()
        
        return results
    
    def _connect_to_database(self) -> bool:
        """
        Establish database connection and initialize components.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.db_connection = DatabaseConnection(self.db_config)
            
            if self.db_connection.connect():
                self.table_creator = TableCreator(self.db_connection)
                self.data_inserter = DataInserter(self.db_connection, self.batch_size)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def analyze_csv_only(self, csv_file_path: str, delimiter: str = ',', 
                        encoding: str = 'utf-8') -> Dict[str, Any]:
        """
        Analyze CSV file without creating table or inserting data.
        
        Args:
            csv_file_path: Path to CSV file
            delimiter: CSV delimiter character
            encoding: File encoding
            
        Returns:
            Analysis results
        """
        try:
            return self.analyzer.analyze_csv(csv_file_path, delimiter, encoding)
        except Exception as e:
            logger.error(f"CSV analysis failed: {e}")
            return {'error': str(e)}
    
    def generate_create_table_sql(self, csv_file_path: str, table_name: str,
                                delimiter: str = ',', encoding: str = 'utf-8') -> str:
        """
        Generate CREATE TABLE SQL without executing it.
        
        Args:
            csv_file_path: Path to CSV file
            table_name: Name for the table
            delimiter: CSV delimiter character
            encoding: File encoding
            
        Returns:
            CREATE TABLE SQL statement
        """
        try:
            analysis = self.analyzer.analyze_csv(csv_file_path, delimiter, encoding)
            return self.analyzer.generate_create_table_sql(analysis, table_name)
        except Exception as e:
            logger.error(f"SQL generation failed: {e}")
            return f"-- Error generating SQL: {e}"


def main():
    """Command line interface for CSV to Database tool."""
    parser = argparse.ArgumentParser(description='Automate CSV file loading into PostgreSQL database')
    parser.add_argument('csv_file', help='Path to CSV file')
    parser.add_argument('table_name', help='Name for the database table')
    parser.add_argument('--delimiter', default=',', help='CSV delimiter (default: comma)')
    parser.add_argument('--encoding', default='utf-8', help='File encoding (default: utf-8)')
    parser.add_argument('--drop-if-exists', action='store_true', 
                       help='Drop table if it already exists')
    parser.add_argument('--skip-errors', action='store_true',
                       help='Skip rows with errors during insertion')
    parser.add_argument('--no-indexes', action='store_true',
                       help='Do not create indexes')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze CSV file, do not create table or insert data')
    parser.add_argument('--generate-sql', action='store_true',
                       help='Generate CREATE TABLE SQL and exit')
    parser.add_argument('--sample-size', type=int, default=1000,
                       help='Number of rows to sample for analysis (0 = all rows)')
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='Number of rows to insert per batch')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = CSVToDatabase(sample_size=args.sample_size, batch_size=args.batch_size)
    
    if args.analyze_only:
        # Analyze only
        analysis = processor.analyze_csv_only(args.csv_file, args.delimiter, args.encoding)
        print("CSV Analysis Results:")
        print(f"Rows: {analysis.get('total_rows', 'N/A')}")
        print(f"Columns: {analysis.get('total_columns', 'N/A')}")
        if 'columns' in analysis:
            print("\nColumn Types:")
            for name, info in analysis['columns'].items():
                print(f"  {name}: {info['postgresql_type']}")
    
    elif args.generate_sql:
        # Generate SQL only
        sql = processor.generate_create_table_sql(args.csv_file, args.table_name, 
                                                 args.delimiter, args.encoding)
        print("Generated CREATE TABLE SQL:")
        print(sql)
    
    else:
        # Full process
        results = processor.process_csv(
            csv_file_path=args.csv_file,
            table_name=args.table_name,
            delimiter=args.delimiter,
            encoding=args.encoding,
            drop_if_exists=args.drop_if_exists,
            skip_errors=args.skip_errors,
            create_indexes=not args.no_indexes
        )
        
        # Print results
        if results['success']:
            print(f"✅ Successfully processed {args.csv_file}")
            print(f"📊 Inserted {results['data_insertion']['successful_rows']} rows into table '{args.table_name}'")
            print(f"⏱️  Total duration: {results['total_duration']}")
        else:
            print(f"❌ Process failed: {results['error']}")
            exit(1)


if __name__ == "__main__":
    main()
