"""
Test script to verify the CSV to PostgreSQL automation setup.
Run this to check if all components are working correctly.
"""

import os
import sys
from csv_to_db import CSVToDatabase

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    try:
        import pandas
        import psycopg2
        import numpy
        from dotenv import load_dotenv
        print("✅ All required packages imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def test_csv_analysis():
    """Test CSV analysis functionality."""
    print("\nTesting CSV analysis...")
    try:
        processor = CSVToDatabase()
        analysis = processor.analyze_csv_only('example_data.csv')
        
        print(f"✅ CSV analysis successful:")
        print(f"   - Rows: {analysis['total_rows']}")
        print(f"   - Columns: {analysis['total_columns']}")
        print(f"   - Detected types: {len(analysis['columns'])}")
        
        return True
    except Exception as e:
        print(f"❌ CSV analysis failed: {e}")
        return False

def test_sql_generation():
    """Test SQL generation functionality."""
    print("\nTesting SQL generation...")
    try:
        processor = CSVToDatabase()
        sql = processor.generate_create_table_sql('example_data.csv', 'test_table')
        
        if sql and 'CREATE TABLE' in sql:
            print("✅ SQL generation successful")
            print("Generated SQL preview:")
            print(sql[:200] + "..." if len(sql) > 200 else sql)
            return True
        else:
            print("❌ SQL generation failed - no valid SQL returned")
            return False
    except Exception as e:
        print(f"❌ SQL generation failed: {e}")
        return False

def test_env_file():
    """Test environment file configuration."""
    print("\nTesting environment configuration...")
    
    if os.path.exists('.env'):
        print("✅ .env file found")
        
        # Load and check basic variables
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
            print("   Please configure these in your .env file")
            return False
        else:
            print("✅ All required environment variables configured")
            return True
    else:
        print("⚠️  .env file not found")
        print("   Copy .env.example to .env and configure your database settings")
        return False

def test_database_connection():
    """Test database connection (optional - requires valid credentials)."""
    print("\nTesting database connection...")
    
    if not test_env_file():
        print("⚠️  Skipping database connection test - environment not configured")
        return False
    
    try:
        from database_connection import DatabaseConnection
        
        db = DatabaseConnection()
        if db.connect():
            print("✅ Database connection successful")
            db.disconnect()
            return True
        else:
            print("❌ Database connection failed")
            print("   Check your database credentials and ensure PostgreSQL is running")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def main():
    """Run all tests."""
    print("CSV to PostgreSQL Automation - Setup Test")
    print("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("CSV Analysis", test_csv_analysis),
        ("SQL Generation", test_sql_generation),
        ("Environment Config", test_env_file),
        ("Database Connection", test_database_connection)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your setup is ready to use.")
        print("\nNext steps:")
        print("1. Configure your .env file with database credentials")
        print("2. Run: python csv_to_db.py example_data.csv employees")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        
        if not results.get("Package Imports", False):
            print("\nTo fix package imports:")
            print("pip install -r requirements.txt")
        
        if not results.get("Environment Config", False):
            print("\nTo fix environment config:")
            print("cp .env.example .env")
            print("# Then edit .env with your database settings")

if __name__ == "__main__":
    main()
